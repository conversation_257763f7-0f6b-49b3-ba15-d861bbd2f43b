import pandas as pd
import requests
import json
import time
from typing import Dict, Any

def generate_course_description(title: str, max_retries: int = 3, timeout: int = 12000) -> str:
    """
    Generate a course description using the local Mathstral model.
    
    Args:
        title (str): The course title
        max_retries (int): Maximum number of retry attempts
        timeout (int): Request timeout in seconds
        
    Returns:
        str: The generated course description
    """
    
    system_prompt = """You are a Course Description Generator. Generate a brief, engaging description for the given course title. The description should be a single paragraph that highlights what the course covers and why it's interesting or valuable. Respond with a JSON object containing only the description, like this:
{
  "description": "[Your generated description here]"
}"""
    
    payload = {
        "model": "mistralai/mathstral-7b-v0.1",
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"Generate a course description for: {title}"}
        ],
        "temperature": 0.7,
        "max_tokens": 256,
        "stream": False
    }
    
    for attempt in range(max_retries):
        try:
            response = requests.post(
                "http://localhost:1234/v1/chat/completions",
                headers={"Content-Type": "application/json"},
                json=payload,
                timeout=timeout
            )
            
            if response.status_code == 200:
                response_data = response.json()
                content = response_data['choices'][0]['message']['content']
                
                try:
                    description_json = json.loads(content)
                    if "description" in description_json:
                        return description_json["description"]
                    else:
                        print(f"Missing 'description' key in response for title: {title}")
                except json.JSONDecodeError:
                    print(f"Failed to parse JSON for title: {title}")
                
                if attempt == max_retries - 1:
                    return "Error generating course description."
            else:
                print(f"API request failed for title: {title}, Status code: {response.status_code}")
                if attempt == max_retries - 1:
                    return "Error: API request failed"
            
            time.sleep(2 ** attempt)
        except requests.exceptions.RequestException as e:
            print(f"Request exception for title: {title}, Error: {str(e)}")
            if attempt == max_retries - 1:
                return "Error: Request exception"
            time.sleep(2 ** attempt)
    
    return "Error: Maximum retry attempts reached"

def process_csv_file(input_file: str = "learning_path_rows.csv", output_file: str = "updated_courses.csv"):
    """
    Process the CSV file and add course descriptions, saving only ID and description to the output.
    
    Args:
        input_file (str): Path to the input CSV file
        output_file (str): Path to the output CSV file
    """
    
    try:
        print(f"Reading CSV file: {input_file}")
        df = pd.read_csv(input_file)
        
        if 'title' not in df.columns:
            print("Error: 'title' column not found in the CSV file")
            print(f"Available columns: {list(df.columns)}")
            return
        
        # Assuming 'created_at' is the ID column based on sample data
        if 'created_at' not in df.columns:
            print("Error: 'created_at' (assumed ID) column not found in the CSV file")
            print(f"Available columns: {list(df.columns)}")
            return
        
        total_rows = len(df)
        print(f"Found {total_rows} rows in the CSV file")
        
        start_time = time.time()
        processed_count = 0
        
        # Initialize output DataFrame with ID and description
        output_df = pd.DataFrame({
            'id': df['created_at'],
            'description': [''] * total_rows
        })
        
        # Check if output file exists to resume processing
        try:
            existing_df = pd.read_csv(output_file)
            if len(existing_df) == total_rows and 'description' in existing_df.columns and 'id' in existing_df.columns:
                start_index = 0
                for i, row in existing_df.iterrows():
                    if pd.notna(row['description']) and row['description'] != '':
                        start_index = i + 1
                        processed_count = start_index
                if start_index < total_rows:
                    print(f"Resuming from row {start_index+1} of {total_rows}")
                    output_df['description'] = existing_df['description']
                else:
                    print("All rows already processed!")
                    return
            else:
                print("Starting new processing")
                start_index = 0
        except (FileNotFoundError, pd.errors.EmptyDataError):
            print("No existing output file found. Starting from beginning.")
            start_index = 0
        
        try:
            for index, row in df.iloc[start_index:].iterrows():
                title = row['title'].strip() if pd.notna(row['title']) else ''
                
                if title:
                    processed_count += 1
                    elapsed_time = time.time() - start_time
                    items_per_second = processed_count / elapsed_time if elapsed_time > 0 else 0
                    estimated_remaining = (total_rows - processed_count) / items_per_second if items_per_second > 0 else 0
                    
                    print(f"Processing ({index+1}/{total_rows}): {title}")
                    print(f"Progress: {processed_count}/{total_rows-start_index} | " 
                          f"Speed: {items_per_second:.2f} items/sec | "
                          f"Est. remaining: {estimated_remaining/60:.1f} minutes")
                    
                    description = generate_course_description(title)
                    output_df.at[index, 'description'] = description
                    output_df.to_csv(output_file, index=False)
                else:
                    print(f"Skipping row {index+1}: Empty or missing title")
                    output_df.at[index, 'description'] = "Error: Empty or missing title"
                    output_df.to_csv(output_file, index=False)
        except KeyboardInterrupt:
            print("\nProcess interrupted by user. Progress has been saved.")
            print(f"Processed {processed_count} out of {total_rows-start_index} remaining rows.")
            print(f"Total progress: {start_index + processed_count}/{total_rows} rows")
            output_df.to_csv(output_file, index=False)
            return
        
        print(f"Saving updated CSV to: {output_file}")
        output_df.to_csv(output_file, index=False)
        
        total_time = time.time() - start_time
        print(f"Process completed successfully in {total_time/60:.1f} minutes!")
        print(f"Updated CSV saved as: {output_file}")
        
    except FileNotFoundError:
        print(f"Error: File '{input_file}' not found")
    except pd.errors.EmptyDataError:
        print("Error: The CSV file is empty")
    except Exception as e:
        print(f"Error processing CSV file: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    print("Course Description Generator")
    print("=" * 50)
    
    input_csv = "learning_path_rows.csv"
    output_csv = "updated_courses.csv"
    
    process_csv_file(input_csv, output_csv)

if __name__ == "__main__":
    main()