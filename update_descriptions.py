import pandas as pd
import requests
import json
import time
import sys
from typing import Dict, Any, Optional

# Supabase configuration
SUPABASE_URL = "https://tkhqppfqsitovjvsstfl.supabase.co/rest/v1/learning_path"
API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRraHFwcGZxc2l0b3ZqdnNzdGZsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQxMTE0MTYsImV4cCI6MjA0OTY4NzQxNn0.A9_h-49niRCpWgD83_xHRQr-pCpKOAikbx0YZIOYa4Q"

def update_learning_path_description(course_id: str, description: str, max_retries: int = 3) -> bool:
    """
    Update the description for a specific course in the learning_path table.
    
    Args:
        course_id (str): The ID of the course to update
        description (str): The new description
        max_retries (int): Maximum number of retry attempts
        
    Returns:
        bool: True if successful, False otherwise
    """
    
    headers = {
        "apikey": API_KEY,
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json",
        "Prefer": "return=minimal"
    }
    
    # Construct the URL with the ID filter
    url = f"{SUPABASE_URL}?id=eq.{course_id}"
    
    # Prepare the data payload
    payload = {
        "description": description
    }
    
    for attempt in range(max_retries):
        try:
            response = requests.patch(
                url,
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 204:
                # Success - Supabase returns 204 No Content for successful updates
                return True
            elif response.status_code == 200:
                # Some APIs return 200 for successful updates
                return True
            elif response.status_code == 404:
                print(f"⚠️  Course ID {course_id} not found in database")
                return False
            else:
                print(f"❌ API request failed for ID {course_id}: Status {response.status_code}")
                if response.text:
                    print(f"   Response: {response.text}")
                
                if attempt == max_retries - 1:
                    return False
                    
        except requests.exceptions.RequestException as e:
            print(f"❌ Request exception for ID {course_id}: {str(e)}")
            if attempt == max_retries - 1:
                return False
        
        # Wait before retrying (exponential backoff)
        if attempt < max_retries - 1:
            wait_time = 2 ** attempt
            print(f"   Retrying in {wait_time} seconds...")
            time.sleep(wait_time)
    
    return False

def process_combined_courses(
    input_file: str = "combined_courses.csv",
    start_from: int = 0,
    batch_size: int = 10
) -> None:
    """
    Process the combined courses CSV file and update descriptions in Supabase.
    
    Args:
        input_file (str): Path to the combined courses CSV file
        start_from (int): Row index to start from (for resuming)
        batch_size (int): Number of records to process before showing progress
    """
    
    try:
        print(f"📖 Reading data from: {input_file}")
        df = pd.read_csv(input_file)
        
        if 'id' not in df.columns or 'description' not in df.columns:
            print("❌ Error: Required columns 'id' and 'description' not found")
            print(f"Available columns: {list(df.columns)}")
            return
        
        total_rows = len(df)
        print(f"📊 Found {total_rows} courses to update")
        
        if start_from > 0:
            print(f"🔄 Resuming from row {start_from + 1}")
            df = df.iloc[start_from:]
        
        successful_updates = 0
        failed_updates = 0
        start_time = time.time()
        
        print(f"\n🚀 Starting updates...")
        print("=" * 80)
        
        for index, row in df.iterrows():
            course_id = row['id']
            description = row['description']
            
            # Skip rows with missing data
            if pd.isna(course_id) or pd.isna(description):
                print(f"⏭️  Skipping row {index + 1}: Missing ID or description")
                failed_updates += 1
                continue
            
            # Skip error descriptions
            if description.startswith("Error"):
                print(f"⏭️  Skipping row {index + 1}: Error description")
                failed_updates += 1
                continue
            
            print(f"🔄 Updating course {index + 1}/{total_rows}: {course_id}")
            
            # Update the description
            success = update_learning_path_description(course_id, description)
            
            if success:
                successful_updates += 1
                print(f"✅ Successfully updated course {course_id}")
            else:
                failed_updates += 1
                print(f"❌ Failed to update course {course_id}")
            
            # Show progress every batch_size records
            if (index + 1) % batch_size == 0:
                elapsed_time = time.time() - start_time
                rate = (successful_updates + failed_updates) / elapsed_time
                remaining = total_rows - (index + 1)
                eta = remaining / rate if rate > 0 else 0
                
                print(f"\n📈 Progress Report:")
                print(f"   Processed: {index + 1}/{total_rows}")
                print(f"   Successful: {successful_updates}")
                print(f"   Failed: {failed_updates}")
                print(f"   Rate: {rate:.2f} updates/second")
                print(f"   ETA: {eta/60:.1f} minutes")
                print("-" * 80)
            
            # Small delay to avoid overwhelming the API
            time.sleep(0.5)
        
        # Final summary
        total_time = time.time() - start_time
        print(f"\n🎉 Update process completed!")
        print("=" * 80)
        print(f"📊 Final Statistics:")
        print(f"   Total processed: {successful_updates + failed_updates}")
        print(f"   Successful updates: {successful_updates}")
        print(f"   Failed updates: {failed_updates}")
        print(f"   Success rate: {(successful_updates/(successful_updates + failed_updates)*100):.1f}%")
        print(f"   Total time: {total_time/60:.1f} minutes")
        print(f"   Average rate: {(successful_updates + failed_updates)/total_time:.2f} updates/second")
        
    except FileNotFoundError:
        print(f"❌ Error: File '{input_file}' not found")
        print("💡 Make sure the combined_courses.csv file exists in the current directory")
        
    except pd.errors.EmptyDataError:
        print("❌ Error: The CSV file is empty")
        
    except KeyboardInterrupt:
        print(f"\n⏹️  Process interrupted by user")
        print(f"📊 Progress so far:")
        print(f"   Successful updates: {successful_updates}")
        print(f"   Failed updates: {failed_updates}")
        
    except Exception as e:
        print(f"❌ Error processing file: {str(e)}")
        import traceback
        traceback.print_exc()

def test_api_connection() -> bool:
    """
    Test the API connection by making a simple GET request.
    
    Returns:
        bool: True if connection is successful, False otherwise
    """
    
    headers = {
        "apikey": API_KEY,
        "Authorization": f"Bearer {API_KEY}",
    }
    
    try:
        # Test with a limit of 1 to minimize data transfer
        test_url = f"{SUPABASE_URL}?limit=1"
        response = requests.get(test_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ API connection successful")
            return True
        else:
            print(f"❌ API connection failed: Status {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ API connection error: {str(e)}")
        return False

def show_usage():
    """Display usage information for the script."""
    print("\n📖 Usage:")
    print("  python update_descriptions.py")
    print("  python update_descriptions.py <csv_file>")
    print("  python update_descriptions.py <csv_file> <start_from_row>")
    print("\n📝 Examples:")
    print("  python update_descriptions.py")
    print("  python update_descriptions.py combined_courses.csv")
    print("  python update_descriptions.py combined_courses.csv 50")
    print("\n🔧 Options:")
    print("  csv_file: Path to the combined courses CSV file (default: combined_courses.csv)")
    print("  start_from_row: Row number to start from (useful for resuming, 0-based index)")

def main():
    """Main function to run the description update script."""
    
    print("🔄 Learning Path Description Updater")
    print("=" * 60)
    print("This script updates course descriptions in Supabase")
    print("using data from combined_courses.csv")
    print("=" * 60)
    
    # Check if help is requested
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_usage()
        return
    
    # Parse command line arguments
    csv_file = "combined_courses.csv"
    start_from = 0
    
    if len(sys.argv) >= 2:
        csv_file = sys.argv[1]
    
    if len(sys.argv) >= 3:
        try:
            start_from = int(sys.argv[2])
        except ValueError:
            print("❌ Error: start_from_row must be a number")
            show_usage()
            return
    
    print(f"📁 Input file: {csv_file}")
    if start_from > 0:
        print(f"🔄 Starting from row: {start_from + 1}")
    
    # Test API connection first
    print(f"\n🔗 Testing API connection...")
    if not test_api_connection():
        print("❌ Cannot proceed without API connection")
        return
    
    # Confirm before proceeding
    print(f"\n⚠️  This will update descriptions in the Supabase learning_path table")
    confirm = input("Do you want to continue? (y/N): ").lower().strip()
    
    if confirm not in ['y', 'yes']:
        print("❌ Operation cancelled by user")
        return
    
    # Run the update process
    process_combined_courses(csv_file, start_from)

if __name__ == "__main__":
    main()
