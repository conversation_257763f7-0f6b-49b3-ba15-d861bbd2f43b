# Learning Path Description Update Guide

This guide explains how to update course descriptions in your Supabase `learning_path` table using the generated descriptions from `combined_courses.csv`.

## Overview

The update process involves two scripts:
1. **`test_update.py`** - Tests API connection and previews updates (run this first!)
2. **`update_descriptions.py`** - Performs the actual bulk updates

## Prerequisites

- Python 3.6+ with `pandas` and `requests` libraries
- `combined_courses.csv` file (generated by `combine_csv.py`)
- Valid Supabase API credentials
- Internet connection

## Step-by-Step Process

### Step 1: Test the API Connection

**Always run the test script first!**

```bash
python test_update.py
```

This script will:
- ✅ Test your Supabase API connection
- ✅ Show the current database structure
- ✅ Preview what updates would be made
- ✅ Optionally test updating a single record

### Step 2: Run the Full Update

Once testing is successful:

```bash
python update_descriptions.py
```

## Script Features

### `test_update.py`
- **Safe testing** - No bulk changes
- **API validation** - Confirms connection works
- **Data preview** - Shows first 5 records that would be updated
- **Single update test** - Tests one record to verify everything works
- **Database structure inspection** - Shows available fields

### `update_descriptions.py`
- **Bulk updates** - Processes all records in the CSV
- **Progress tracking** - Shows real-time progress and statistics
- **Error handling** - Gracefully handles API failures and retries
- **Resume capability** - Can restart from a specific row if interrupted
- **Rate limiting** - Includes delays to avoid overwhelming the API
- **Comprehensive logging** - Detailed success/failure reporting

## Usage Examples

### Basic Usage
```bash
# Test first
python test_update.py

# Then update all
python update_descriptions.py
```

### Advanced Usage
```bash
# Use custom CSV file
python update_descriptions.py my_courses.csv

# Resume from row 50 (if process was interrupted)
python update_descriptions.py combined_courses.csv 50

# Show help
python update_descriptions.py --help
```

## API Configuration

The scripts use these Supabase settings:
- **URL**: `https://tkhqppfqsitovjvsstfl.supabase.co/rest/v1/learning_path`
- **API Key**: Embedded in the scripts
- **Method**: PATCH requests to update descriptions
- **Filter**: Updates by course ID (`id=eq.{course_id}`)

## What Gets Updated

The script will:
- ✅ Read course IDs and descriptions from `combined_courses.csv`
- ✅ Update the `description` field in the `learning_path` table
- ✅ Match records using the `id` field
- ⏭️ Skip records with "Error" descriptions
- ⏭️ Skip records with missing data

## Safety Features

### Before Running Updates:
- API connection test
- Data validation
- User confirmation prompt
- Preview of changes

### During Updates:
- Retry logic for failed requests
- Rate limiting to avoid API overload
- Progress tracking and reporting
- Graceful error handling

### Error Handling:
- Network timeouts
- API rate limits
- Invalid course IDs
- Missing data
- Interrupted processes

## Expected Output

### Successful Update:
```
✅ Successfully updated course 95dca2d0-8c97-401d-8c16-5d4891f1073c
```

### Progress Reports:
```
📈 Progress Report:
   Processed: 50/178
   Successful: 48
   Failed: 2
   Rate: 2.5 updates/second
   ETA: 3.2 minutes
```

### Final Summary:
```
🎉 Update process completed!
📊 Final Statistics:
   Total processed: 178
   Successful updates: 175
   Failed updates: 3
   Success rate: 98.3%
   Total time: 4.2 minutes
```

## Troubleshooting

### Common Issues:

1. **API Connection Failed**
   - Check internet connection
   - Verify API key is correct
   - Ensure Supabase service is running

2. **Course ID Not Found**
   - The course may not exist in the database
   - Check if the ID format matches

3. **Permission Denied**
   - Verify API key has write permissions
   - Check Supabase RLS (Row Level Security) policies

4. **Rate Limiting**
   - The script includes automatic delays
   - If issues persist, increase the delay in the script

### Recovery Options:

- **Resume from specific row**: Use the start_from parameter
- **Check logs**: Review console output for specific error details
- **Test single record**: Use `test_update.py` to isolate issues

## Best Practices

1. **Always test first** with `test_update.py`
2. **Backup your data** before running bulk updates
3. **Run during low-traffic periods** to minimize impact
4. **Monitor progress** and be prepared to stop if needed
5. **Keep the CSV file** as a backup of your descriptions

## File Requirements

### Input File (`combined_courses.csv`):
- Must have `id` and `description` columns
- IDs should match those in your Supabase table
- Descriptions should be properly formatted text

### Dependencies:
```bash
pip install pandas requests
```

## Security Notes

- API keys are embedded in the scripts (consider using environment variables for production)
- The scripts use HTTPS for all API calls
- No sensitive data is logged to console
- Failed updates don't expose credentials

## Support

If you encounter issues:
1. Run `test_update.py` to diagnose the problem
2. Check the console output for specific error messages
3. Verify your CSV file format and content
4. Ensure your Supabase configuration allows updates
