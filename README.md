# CSV File Combiner

This Python script combines data from two CSV files to create a new CSV file with IDs from one file and descriptions from another.

## Purpose

The script takes:
- **IDs** from `learning_path_rows.csv` (specifically the `id` column)
- **Descriptions** from `updated_courses.csv` (the `description` column)

And creates a new CSV file `combined_courses.csv` that contains both pieces of information matched together.

## Files Involved

### Input Files:
1. **`learning_path_rows.csv`** - Contains course information with unique IDs
2. **`updated_courses.csv`** - Contains course descriptions generated by AI

### Output File:
- **`combined_courses.csv`** - Contains the combined data with ID and description columns

## How It Works

The script matches records using timestamps:
- Uses the `created_at` field from `learning_path_rows.csv`
- Matches it with the `id` field in `updated_courses.csv`
- Creates a new file with the original `id` from learning paths and the corresponding `description`

## Usage

### Basic Usage (Default Files)
```bash
python combine_csv.py
```

### Custom File Paths
```bash
python combine_csv.py <learning_path_file> <updated_courses_file> <output_file>
```

### Examples
```bash
# Use default files
python combine_csv.py

# Use custom files
python combine_csv.py data/learning.csv data/courses.csv output/combined.csv

# Show help
python combine_csv.py --help
```

## Features

- ✅ **Automatic matching** of records based on timestamps
- ✅ **Error handling** for missing files and invalid data
- ✅ **Progress reporting** with detailed statistics
- ✅ **Flexible file paths** - use default or custom file locations
- ✅ **Data validation** to ensure required columns exist
- ✅ **Missing data handling** - fills in "No description available" for unmatched records

## Output

The script creates a CSV file with two columns:
- `id` - The unique identifier from the learning path file
- `description` - The AI-generated course description

## Sample Output

```
id,description
95dca2d0-8c97-401d-8c16-5d4891f1073c,"This course is designed to explore the intersection of data science and fashion retail..."
e3434fad-912d-47ed-93bc-e8f1b853f1e6,"Explore the dynamic intersection of Artificial Intelligence (AI) and disaster management..."
```

## Requirements

- Python 3.6+
- pandas library

Install pandas if needed:
```bash
pip install pandas
```

## Success Metrics

When the script runs successfully, you'll see:
- ✅ Number of rows processed
- ✅ Number of successful matches
- ✅ Sample of the combined data
- ✅ Output file location

## Error Handling

The script handles common issues:
- Missing input files
- Empty CSV files
- Missing required columns
- Invalid file formats
- Permission errors

## Notes

- The script preserves the original ID format from `learning_path_rows.csv`
- Descriptions are taken exactly as they appear in `updated_courses.csv`
- If a description is missing, it will show "No description available"
- The output file is saved in CSV format with proper escaping for special characters
