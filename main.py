import pandas as pd
import requests
import json
import time
from typing import Dict, Any

def generate_course_overview(title: str, max_retries: int = 3, timeout: int = 12000) -> Dict[str, Any]:
    """
    Generate a course overview using the local Mathstral model.
    
    Args:
        title (str): The course title
        max_retries (int): Maximum number of retry attempts
        timeout (int): Request timeout in seconds
        
    Returns:
        Dict[str, Any]: The generated course overview in JSON format
    """
    
    # Simplified system prompt for efficiency
    system_prompt = """You are a Course Overview Generator. Generate detailed course overviews in JSON format based on course titles.
Use this exact JSON structure:
{
  "title": "[Course Name] Learning Journey",
  "course_overview": "[Detailed overview paragraph]",
  "what_youll_learn": [
    "[Learning objective 1]",
    "[Learning objective 2]",
    "[Learning objective 3]",
    "[Learning objective 4]",
    "[Learning objective 5]"
  ],
  "who_should_enroll": [
    "[Target audience 1]",
    "[Target audience 2]",
    "[Target audience 3]"
  ],
  "course_format": {
    "duration": "[Duration of the Course]",
    "mode": "[Delivery method with specific details]",
    "assessment": "[Assessment methods and requirements]"
  },
  "why_take_this_course": "[Compelling value proposition paragraph]"
}
Respond only with the properly formatted JSON object."""

    # API request payload
    payload = {
        "model": "mistralai/mathstral-7b-v0.1",
        "messages": [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": f"Generate a course overview for: {title}"}
        ],
        "temperature": 0.7,
        "max_tokens": 1024,  # Limit token count for faster response
        "stream": False
    }
    
    for attempt in range(max_retries):
        try:
            # Make API request to local model
            print(f"Attempt {attempt + 1}/{max_retries} for title: {title}")
            response = requests.post(
                "http://localhost:1234/v1/chat/completions",
                headers={"Content-Type": "application/json"},
                json=payload,
                timeout=timeout
            )
            
            if response.status_code == 200:
                response_data = response.json()
                content = response_data['choices'][0]['message']['content']
                
                # Try to parse the JSON response
                try:
                    # First attempt: direct JSON parsing
                    course_overview = json.loads(content)
                    return course_overview
                except json.JSONDecodeError:
                    # Second attempt: extract JSON from markdown code blocks
                    try:
                        # Look for JSON between code blocks or brackets
                        import re
                        json_pattern = r'```(?:json)?\s*([\s\S]*?)\s*```|(\{[\s\S]*\})'
                        matches = re.findall(json_pattern, content)
                        
                        for match in matches:
                            # Try each potential JSON match
                            for potential_json in match:
                                if potential_json.strip():
                                    try:
                                        course_overview = json.loads(potential_json)
                                        return course_overview
                                    except:
                                        continue
                        
                        # If we get here, no valid JSON was found
                        print(f"Failed to parse JSON for title: {title}")
                        if attempt == max_retries - 1:
                            # Return a simplified error response on final attempt
                            return {
                                "title": f"{title} Learning Journey",
                                "course_overview": "Error generating course overview.",
                                "what_youll_learn": ["Content not available"],
                                "who_should_enroll": ["Content not available"],
                                "course_format": {
                                    "duration": "Not specified",
                                    "mode": "Not specified",
                                    "assessment": "Not specified"
                                },
                                "why_take_this_course": "Content not available"
                            }
                    except Exception as e:
                        print(f"Error extracting JSON: {str(e)}")
                        if attempt == max_retries - 1:
                            return {"error": "Failed to parse JSON response"}
            else:
                print(f"API request failed for title: {title}, Status code: {response.status_code}")
                if attempt == max_retries - 1:
                    return {"error": f"API request failed with status {response.status_code}"}
                
            # Wait before retrying with exponential backoff
            if attempt < max_retries - 1:
                wait_time = 2 ** attempt
                print(f"Waiting {wait_time} seconds before retrying...")
                time.sleep(wait_time)
                
        except requests.exceptions.RequestException as e:
            print(f"Request exception for title: {title}, Error: {str(e)}")
            if attempt == max_retries - 1:
                return {"error": f"Request exception: {str(e)}"}
            
            # Wait before retrying
            wait_time = 2 ** attempt
            print(f"Waiting {wait_time} seconds before retrying...")
            time.sleep(wait_time)
            
        except Exception as e:
            print(f"Unexpected error for title: {title}, Error: {str(e)}")
            if attempt == max_retries - 1:
                return {"error": f"Unexpected error: {str(e)}"}
            
            wait_time = 2 ** attempt
            print(f"Waiting {wait_time} seconds before retrying...")
            time.sleep(wait_time)
    
    # This should not be reached, but just in case
    return {"error": "Maximum retry attempts reached"}

def process_csv_file(input_file: str = "test.csv", output_file: str = "updated_courses.csv"):
    """
    Process the CSV file and add course overviews.
    
    Args:
        input_file (str): Path to the input CSV file
        output_file (str): Path to the output CSV file
    """
    
    try:
        # Read the CSV file
        print(f"Reading CSV file: {input_file}")
        df = pd.read_csv(input_file)
        
        # Check if 'title' column exists
        if 'title' not in df.columns:
            print("Error: 'title' column not found in the CSV file")
            print(f"Available columns: {list(df.columns)}")
            return
        
        total_rows = len(df)
        print(f"Found {total_rows} rows in the CSV file")
        
        # Initialize progress tracking
        start_time = time.time()
        processed_count = 0
        
        # Check if output file exists to determine if we're resuming
        try:
            output_df = pd.read_csv(output_file)
            if len(output_df) == total_rows and 'course_overview' in output_df.columns:
                # Find the last processed row with valid content
                start_index = 0
                for i, row in output_df.iterrows():
                    if pd.notna(row['course_overview']) and row['course_overview'] != '':
                        start_index = i + 1
                        processed_count = start_index
                
                if start_index < total_rows:
                    print(f"Resuming from row {start_index+1} of {total_rows}")
                    # Copy already processed data
                    df['course_overview'] = output_df['course_overview']
                else:
                    print("All rows already processed!")
                    return
            else:
                print("Starting new processing")
                df['course_overview'] = ''
                start_index = 0
        except (FileNotFoundError, pd.errors.EmptyDataError):
            print("No existing output file found. Starting from beginning.")
            df['course_overview'] = ''
            start_index = 0
        
        # Process each row
        try:
            for index, row in df.iloc[start_index:].iterrows():
                title = row['title'].strip() if pd.notna(row['title']) else ''
                
                if title:
                    # Calculate and display progress
                    processed_count += 1
                    elapsed_time = time.time() - start_time
                    items_per_second = processed_count / elapsed_time if elapsed_time > 0 else 0
                    estimated_remaining = (total_rows - processed_count) / items_per_second if items_per_second > 0 else 0
                    
                    print(f"Processing ({index+1}/{total_rows}): {title}")
                    print(f"Progress: {processed_count}/{total_rows-start_index} | " 
                          f"Speed: {items_per_second:.2f} items/sec | "
                          f"Est. remaining: {estimated_remaining/60:.1f} minutes")
                    
                    # Generate course overview
                    overview = generate_course_overview(title)
                    
                    # Convert to JSON string for storage
                    df.at[index, 'course_overview'] = json.dumps(overview, ensure_ascii=False)
                    
                    # Save after each row to allow resuming if interrupted
                    df.to_csv(output_file, index=False)
                else:
                    print(f"Skipping row {index+1}: Empty or missing title")
                    df.at[index, 'course_overview'] = json.dumps({"error": "Empty or missing title"})
                    df.to_csv(output_file, index=False)
        except KeyboardInterrupt:
            print("\nProcess interrupted by user. Progress has been saved.")
            print(f"Processed {processed_count} out of {total_rows-start_index} remaining rows.")
            print(f"Total progress: {start_index + processed_count}/{total_rows} rows")
            # Save one last time before exiting
            df.to_csv(output_file, index=False)
            return
        
        # Final save
        print(f"Saving updated CSV to: {output_file}")
        df.to_csv(output_file, index=False)
        
        # Calculate total time
        total_time = time.time() - start_time
        print(f"Process completed successfully in {total_time/60:.1f} minutes!")
        print(f"Updated CSV saved as: {output_file}")
        
    except FileNotFoundError:
        print(f"Error: File '{input_file}' not found")
    except pd.errors.EmptyDataError:
        print("Error: The CSV file is empty")
    except Exception as e:
        print(f"Error processing CSV file: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """
    Main function to run the course overview generator.
    """
    print("Course Overview Generator")
    print("=" * 50)
    
    # You can customize these file paths
    input_csv = "learning_path_rows.csv"
    output_csv = "updated_courses.csv"
    
    # Process the CSV file
    process_csv_file(input_csv, output_csv)

if __name__ == "__main__":
    main()
