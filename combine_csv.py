import pandas as pd
import sys
from typing import Optional

def combine_csv_files(
    learning_path_file: str = "learning_path_rows.csv",
    updated_courses_file: str = "updated_courses.csv",
    output_file: str = "combined_courses.csv"
) -> None:
    """
    Combine IDs from learning_path_rows.csv with descriptions from updated_courses.csv.

    Args:
        learning_path_file (str): Path to the learning path CSV file
        updated_courses_file (str): Path to the updated courses CSV file
        output_file (str): Path for the output CSV file
    """

    try:
        print(f"Reading learning path data from: {learning_path_file}")
        # Read the learning path CSV file
        learning_df = pd.read_csv(learning_path_file)

        print(f"Reading updated courses data from: {updated_courses_file}")
        # Read the updated courses CSV file
        courses_df = pd.read_csv(updated_courses_file)

        # Display basic info about the files
        print(f"\nLearning path file contains {len(learning_df)} rows")
        print(f"Updated courses file contains {len(courses_df)} rows")

        # Check if required columns exist
        if 'id' not in learning_df.columns:
            print("Error: 'id' column not found in learning path file")
            print(f"Available columns: {list(learning_df.columns)}")
            return

        if 'id' not in courses_df.columns or 'description' not in courses_df.columns:
            print("Error: Required columns not found in updated courses file")
            print(f"Available columns: {list(courses_df.columns)}")
            return

        # Create a new dataframe with id from learning_path and description from updated_courses
        # We'll use the 'id' column from learning_path_rows.csv and match it with 'id' in updated_courses.csv
        print("\nCombining data...")

        # Select only the id column from learning path
        learning_ids = learning_df[['id']].copy()

        # Select id and description from updated courses
        course_descriptions = courses_df[['id', 'description']].copy()

        # Merge the dataframes on 'id'
        # Note: The 'id' in updated_courses.csv appears to be the 'created_at' timestamp
        # We need to match learning_df['created_at'] with courses_df['id']
        if 'created_at' in learning_df.columns:
            print("Using 'created_at' from learning path to match with 'id' in updated courses")
            # Create a mapping dataframe
            learning_mapping = learning_df[['id', 'created_at']].copy()

            # Merge learning_mapping with course_descriptions using created_at = id
            combined_df = pd.merge(
                learning_mapping,
                course_descriptions,
                left_on='created_at',
                right_on='id',
                how='left',
                suffixes=('_learning', '_course')
            )

            # Select final columns: id from learning path and description from courses
            final_df = combined_df[['id_learning', 'description']].copy()
            final_df.columns = ['id', 'description']

        else:
            # Fallback: direct merge on id
            print("Direct merge on 'id' column")
            final_df = pd.merge(
                learning_ids,
                course_descriptions,
                on='id',
                how='left'
            )

        # Fill missing descriptions
        final_df['description'] = final_df['description'].fillna('No description available')

        print(f"\nCombined data contains {len(final_df)} rows")
        print(f"Rows with descriptions: {len(final_df[final_df['description'] != 'No description available'])}")
        print(f"Rows without descriptions: {len(final_df[final_df['description'] == 'No description available'])}")

        # Save the combined data
        print(f"\nSaving combined data to: {output_file}")
        final_df.to_csv(output_file, index=False)

        print("✅ Successfully created combined CSV file!")
        print(f"📁 Output file: {output_file}")

        # Display sample of the output
        print("\n📋 Sample of combined data:")
        print("=" * 80)
        for i, row in final_df.head(3).iterrows():
            print(f"ID: {row['id']}")
            print(f"Description: {row['description'][:100]}...")
            print("-" * 80)

    except FileNotFoundError as e:
        print(f"❌ Error: File not found - {e}")
        print("Please ensure both input files exist in the current directory")

    except pd.errors.EmptyDataError:
        print("❌ Error: One of the CSV files is empty")

    except Exception as e:
        print(f"❌ Error processing files: {str(e)}")
        import traceback
        traceback.print_exc()

def validate_files(learning_path_file: str, updated_courses_file: str) -> bool:
    """
    Validate that the input files exist and are readable.

    Args:
        learning_path_file (str): Path to learning path CSV
        updated_courses_file (str): Path to updated courses CSV

    Returns:
        bool: True if files are valid, False otherwise
    """
    import os

    if not os.path.exists(learning_path_file):
        print(f"❌ Error: Learning path file '{learning_path_file}' not found")
        return False

    if not os.path.exists(updated_courses_file):
        print(f"❌ Error: Updated courses file '{updated_courses_file}' not found")
        return False

    return True

def show_usage():
    """Display usage information for the script."""
    print("\n📖 Usage:")
    print("  python combine_csv.py")
    print("  python combine_csv.py <learning_path_file> <updated_courses_file> <output_file>")
    print("\n📝 Examples:")
    print("  python combine_csv.py")
    print("  python combine_csv.py data/learning.csv data/courses.csv output/combined.csv")

def main():
    """Main function to run the CSV combination script."""

    print("🔄 CSV File Combiner")
    print("=" * 50)
    print("This script combines IDs from learning_path_rows.csv")
    print("with descriptions from updated_courses.csv")
    print("=" * 50)

    # Check if help is requested
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_usage()
        return

    # Check if custom file paths are provided as command line arguments
    if len(sys.argv) == 4:
        learning_path_file = sys.argv[1]
        updated_courses_file = sys.argv[2]
        output_file = sys.argv[3]
        print(f"Using custom file paths:")
        print(f"  Learning path file: {learning_path_file}")
        print(f"  Updated courses file: {updated_courses_file}")
        print(f"  Output file: {output_file}")
    elif len(sys.argv) == 1:
        learning_path_file = "learning_path_rows.csv"
        updated_courses_file = "updated_courses.csv"
        output_file = "combined_courses.csv"
        print(f"Using default file paths:")
        print(f"  Learning path file: {learning_path_file}")
        print(f"  Updated courses file: {updated_courses_file}")
        print(f"  Output file: {output_file}")
    else:
        print("❌ Error: Invalid number of arguments")
        show_usage()
        return

    print()

    # Validate files exist
    if not validate_files(learning_path_file, updated_courses_file):
        print("\n💡 Tip: Make sure the CSV files are in the current directory")
        return

    # Run the combination function
    combine_csv_files(learning_path_file, updated_courses_file, output_file)

if __name__ == "__main__":
    main()
