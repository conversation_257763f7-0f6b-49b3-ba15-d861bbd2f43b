import csv
import json
import requests

# Supabase configuration
SUPABASE_URL = "https://tkhqppfqsitovjvsstfl.supabase.co"
TABLE = "learning_path"
API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRraHFwcGZxc2l0b3ZqdnNzdGZsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQxMTE0MTYsImV4cCI6MjA0OTY4NzQxNn0.A9_h-49niRCpWgD83_xHRQr-pCpKOAikbx0YZIOYa4Q"
AUTHORIZATION = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRraHFwcGZxc2l0b3ZqdnNzdGZsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQxMTE0MTYsImV4cCI6MjA0OTY4NzQxNn0.A9_h-49niRCpWgD83_xHRQr-pCpKOAikbx0YZIOYa4Q"

HEADERS = {
    "apikey": API_KEY,
    "Authorization": f"Bearer {AUTHORIZATION}",
    "Content-Type": "application/json",
    "Prefer": "return=minimal"
}

# Read and update records from CSV (no headers, using tab delimiter)
with open("update.csv", mode="r", newline='', encoding="utf-8") as file:
    reader = csv.reader(file, delimiter="\t")

    for row in reader:
        if len(row) < 2:
            continue  # Skip incomplete rows
        record_id = row[0].strip()
        overview_raw = row[1].strip()

        try:
            overview_json = json.loads(overview_raw)

            url = f"{SUPABASE_URL}/rest/v1/{TABLE}?id=eq.{record_id}"
            payload = json.dumps({ "course_overview": overview_json })

            response = requests.patch(url, headers=HEADERS, data=payload)

            if response.status_code == 204:
                print(f"✅ Updated course_overview for ID: {record_id}")
            else:
                print(f"❌ Failed for ID {record_id}: {response.status_code} - {response.text}")

        except json.JSONDecodeError as e:
            print(f"❌ JSON parse error for ID {record_id}: {e}")
