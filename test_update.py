import pandas as pd
import requests
import json
import sys

# Supabase configuration
SUPABASE_URL = "https://tkhqppfqsitovjvsstfl.supabase.co/rest/v1/learning_path"
API_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRraHFwcGZxc2l0b3ZqdnNzdGZsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzQxMTE0MTYsImV4cCI6MjA0OTY4NzQxNn0.A9_h-49niRCpWgD83_xHRQr-pCpKOAikbx0YZIOYa4Q"

def test_api_connection():
    """Test the API connection and show sample data."""
    
    headers = {
        "apikey": API_KEY,
        "Authorization": f"Bearer {API_KEY}",
    }
    
    try:
        # Test GET request to see current data structure
        test_url = f"{SUPABASE_URL}?limit=3"
        response = requests.get(test_url, headers=headers, timeout=10)
        
        if response.status_code == 200:
            print("✅ API connection successful")
            data = response.json()
            print(f"📊 Found {len(data)} sample records")
            
            if data:
                print("\n📋 Sample record structure:")
                sample = data[0]
                for key, value in sample.items():
                    if isinstance(value, str) and len(value) > 100:
                        value = value[:100] + "..."
                    print(f"  {key}: {value}")
                
                # Check if description field exists
                if 'description' in sample:
                    print(f"\n✅ 'description' field found in database")
                    current_desc = sample.get('description', '')
                    if current_desc:
                        print(f"📝 Current description length: {len(current_desc)} characters")
                    else:
                        print(f"📝 Current description is empty")
                else:
                    print(f"\n❌ 'description' field NOT found in database")
                    print(f"Available fields: {list(sample.keys())}")
            
            return True
        else:
            print(f"❌ API connection failed: Status {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ API connection error: {str(e)}")
        return False

def test_single_update():
    """Test updating a single record to verify the API works."""
    
    print("\n🧪 Testing single record update...")
    
    # Read the first record from combined_courses.csv
    try:
        df = pd.read_csv("combined_courses.csv")
        if len(df) == 0:
            print("❌ No data found in combined_courses.csv")
            return False
        
        first_record = df.iloc[0]
        course_id = first_record['id']
        description = first_record['description']
        
        print(f"🎯 Testing with course ID: {course_id}")
        print(f"📝 Description length: {len(description)} characters")
        
        headers = {
            "apikey": API_KEY,
            "Authorization": f"Bearer {API_KEY}",
            "Content-Type": "application/json",
            "Prefer": "return=minimal"
        }
        
        url = f"{SUPABASE_URL}?id=eq.{course_id}"
        payload = {"description": description}
        
        print(f"🔗 Making PATCH request to: {url}")
        
        response = requests.patch(url, headers=headers, json=payload, timeout=30)
        
        if response.status_code in [200, 204]:
            print("✅ Test update successful!")
            print(f"   Status code: {response.status_code}")
            return True
        else:
            print(f"❌ Test update failed: Status {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Test update error: {str(e)}")
        return False

def preview_updates(limit: int = 5):
    """Preview what updates would be made."""
    
    print(f"\n👀 Previewing first {limit} updates...")
    
    try:
        df = pd.read_csv("combined_courses.csv")
        
        print(f"📊 Total records in CSV: {len(df)}")
        
        preview_df = df.head(limit)
        
        for index, row in preview_df.iterrows():
            course_id = row['id']
            description = row['description']
            
            print(f"\n📝 Record {index + 1}:")
            print(f"   ID: {course_id}")
            print(f"   Description: {description[:100]}...")
            print(f"   Length: {len(description)} characters")
            
            # Check if it's an error description
            if description.startswith("Error"):
                print(f"   ⚠️  This is an error description - would be skipped")
        
        return True
        
    except Exception as e:
        print(f"❌ Preview error: {str(e)}")
        return False

def main():
    """Main function for testing the update functionality."""
    
    print("🧪 Learning Path Update Tester")
    print("=" * 50)
    print("This script tests the Supabase API connection")
    print("and previews updates before running the full script")
    print("=" * 50)
    
    # Test API connection
    print("1️⃣ Testing API connection...")
    if not test_api_connection():
        print("❌ Cannot proceed without API connection")
        return
    
    # Preview updates
    print("\n2️⃣ Previewing updates...")
    if not preview_updates():
        print("❌ Cannot preview updates")
        return
    
    # Ask if user wants to test a single update
    print(f"\n3️⃣ Single update test")
    test_confirm = input("Do you want to test updating one record? (y/N): ").lower().strip()
    
    if test_confirm in ['y', 'yes']:
        if test_single_update():
            print("\n✅ Single update test passed!")
            print("💡 You can now run the full update script with:")
            print("   python update_descriptions.py")
        else:
            print("\n❌ Single update test failed!")
            print("💡 Please check your API credentials and database structure")
    else:
        print("\n💡 Skipped single update test")
        print("💡 To run the full update script:")
        print("   python update_descriptions.py")
    
    print(f"\n📋 Summary:")
    print(f"   ✅ API connection: Working")
    print(f"   ✅ Data preview: Available")
    print(f"   {'✅' if test_confirm in ['y', 'yes'] else '⏭️ '} Single update test: {'Completed' if test_confirm in ['y', 'yes'] else 'Skipped'}")

if __name__ == "__main__":
    main()
